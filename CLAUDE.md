CRITICAL MANDATORY INSTRUCTIONS YOU MUST OBIDE ELSE YOU WILL BREAK THE APP
THESE ARE NOT RECOMMENDATIONS. BELOW ARE INSTRUCTIONS - COMMANDS YOU MUST OBIDE

## MANDATORY WHEN STARTING ANY NEW SESSION:
1. Check current architecture → docs/architecture/
2. Check if similar feature has up to date documentation → docs/features/  
3. Check if similar bug was being solved and fixed → docs/fixes/
4. ONLY IF NOTHING RELEVANT FOUND, THEN search the codebase

## MANDATORY TASK CHECKLIST:
Before starting ANY task, Claude Code MUST:
1. **Identify task type** (testing/UI/API/Vue/translation/authentication/etc.)
2. **Check basic context** before proceeding
3. **Verify against guidelines** before implementing

**CLAUDE CODE MUST CHECK BASIC CONTEXT BASED ON THE TASK TYPE**
- Testing anything? → `docs/development/testing_debugging_guide.md`
- UI/styling work? →  `docs/development/ui_styling_guide.md`
- Authentication/API? → `docs/development/spa_endpoints.md`
- Translations? → `docs/development/translations.md`
If the task does not have previously solved context or guidlines, <PERSON> Code must inform the user.

## Session Tracking
Use TodoWrite for Session Tracking
Use the TodoWrite tool to track not just tasks, but also:
  - Issues created during the session
  - External references (GitHub/Linear issue numbers)
  - Major actions taken

## CRITICAL ARCHITECTURE FILES
- `docs/architecture/architecture_and_implementation_guide.md` - Critical architecture guide
- `POLICIES.md` - Authorization information using ActionPolicy
- `app/frontend/mixins/authorizationMixin.js` - Frontend permissions

## CODE STYLE - NON-NEGOTIABLE
- **ALL code files MUST start with 2-line comment**: "ABOUTME: [what file does]"  
- **NEVER remove code comments** unless provably false - they are critical documentation
- **NEVER name things 'improved', 'new', 'enhanced'** - use evergreen naming
- Make smallest reasonable changes - ASK PERMISSION before reimplementing systems
- Ruby: Follow standard Rails conventions (MVC architecture)
- Models: Use validations, callbacks sparingly, prefer service objects for complex logic
- Controllers: Keep thin, use policies for authorization
- Vue: Use Options API 
- Vue: we have full SPA and full JWT token auth application using router, vuex, axios
- Components: Organize by feature in app/frontend/components/
- State management: Use Vuex stores in app/frontend/store/
- Error handling: Use flash messages via app/frontend/utils/flashMessage.js

## TDD MANDATORY PROCESS
1. Write failing test first
2. Write minimal code to pass test  
3. Refactor while keeping tests green
4. Primary testing: `bundle exec rspec spec/features/`
Important instruction: Convert ALL tests to RSpec format, including existing Rails Test Framework tests. Do not use Minitest/Rails Test Framework under any circumstances, even if files already exist in that format.

## SPA ENDPOINT RULES - MANDATORY
- **ALL data endpoints MUST use `/api/v1/` namespace**
- **NEVER use main controller endpoints** (e.g., `/works/assigned`)
- **ALWAYS use API endpoints** (e.g., `/api/v1/works/assigned`)
- Include: `{ headers: { 'Accept': 'application/json' } }`
- API endpoints use JWT token authentication

## FRONTEND ERROR HANDLING - CRITICAL RULES
- **NEVER duplicate backend error logic in frontend**
- **ALWAYS use backend error messages directly** - they are already perfect and properly formatted
- **NEVER manually parse `error.response.data.errors`** - use backend message as-is
- **NO custom error message creation** - backend already knows the business context
- **USE ONE centralized error handler** - avoid duplicated error handling code
- **RESPECT Rails error/message distinction**: errors = validation issues, messages = informational
- **Frontend should ONLY display what backend provides** - no recreation of business logic
- **Example**: `sendFlashMessage(error.response?.data?.message || 'Error occurred', 'error')`

## STYLING/UI - STRICT RULES
- **NO shadows, animations, CSS that slows page processing**
- **Minimalist design, proper contrast, text readability**
- **First-level visible buttons/actions** - avoid dropdown menus
- **NO EMOJIS** (except development debugging)
- Conservative business app design

## INTERNATIONALIZATION - MANDATORY
- Default translation language is CZECH - cs locale
- **Backend (Rails I18n)**: Error messages, success messages, dynamic messages with interpolated data, business logic messages
- **Frontend (Vue.js $t())**: Static UI labels, form field labels, navigation text, static interface elements
- Backend: Use `I18n.t('controllers.controller_name.messages.key', variable: value)` in controllers/models
- Frontend: Use `$t('key', 'Default text')` in Vue components
- Keys in snake_case, feature-scoped: `$t('booking.cancel', 'Zrušit')`
- **NEVER skip i18n** - always add translation keys

## TOOLING - USE FIRST
- **Use context7** for up-to-date documentation before searching elsewhere
- Double-check critical tasks with Gemini
- We use standard ruby on rails with vue.js app, if you stuck on the circle issue, try to search the web for the similar issue, there is very good chance you will find existing solution  

## DOCUMENTATION - MANDATORY
- All docs in docs/ folder under respective subfolder
- Update feature README when changes made
  - Technical changes
  - New file structure changed
  - Updated setup/usage instructions
- Update Linear project when related Linear issue moving to "Done"
- Do not reference Claude Code in the commit messages, nor in the github issues, nor in the github PRs

## NOTIFICATION PROTOCOL:
IMPORTANT: Before any state where you are awaiting confirmation, awaiting input, awaiting decision, requesting permission, or waiting for review:
1. Send email via Gmail MCP with:
   - Subject: "Claude Code: [STATE] - [BRIEF_CONTEXT]"
   - Body: "Action required in terminal. Check your Claude Code session."
2. Then display the full details and request in the terminal as normal
3. Do not include command details or sensitive information in the email
4. Keep the email brief - just a notification that attention is needed
EXAMPLES:
- Subject: "Claude Code: Awaiting Confirmation - File Deletion"
- Subject: "Claude Code: Awaiting Input - Database Connection"
- Subject: "Claude Code: Requesting Permission - System Modification" 

## Data Attributes - MANDATORY for Vue
<div data-vue-component="component-name">
<button data-action="submit-form" data-testid="submit-button">
<LocalizedLink data-nav-link="dashboard" data-testid="nav-dashboard">





