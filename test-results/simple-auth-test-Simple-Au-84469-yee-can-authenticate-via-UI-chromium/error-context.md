# Test info

- Name: Simple Authentication Test >> employee can authenticate via UI
- Location: /home/<USER>/Projects/attendifyapp-claude/test/e2e/simple-auth-test.spec.ts:25:3

# Error details

```
TimeoutError: page.waitForURL: Timeout 10000ms exceeded.
=========================== logs ===========================
waiting for navigation to "**/cs/dashboard" until "load"
============================================================
    at LoginPage.expectToBeLoggedIn (/home/<USER>/Projects/attendifyapp-claude/test/e2e/pages/LoginPage.ts:38:21)
    at /home/<USER>/Projects/attendifyapp-claude/test/e2e/simple-auth-test.spec.ts:33:21
```

# Page snapshot

```yaml
- img "Týmbox Logo"
- heading "Přihlásit se" [level=1]
- textbox "Váš e-mail": <EMAIL>
- textbox "Vaše heslo": "123456"
- button "ukázat"
- button "Přihlásit se"
- paragraph:
  - link "Bezplatná registrace zde.":
    - /url: /users/sign_up
- paragraph:
  - link "Zapomenuté heslo?":
    - /url: /users/password/new
- paragraph:
  - link "Nedostali jste potvrzovací instrukce?":
    - /url: /cs/users/confirmation/new
- text: Nesprávný email nebo heslo 17814.5 ms×10
```

# Test source

```ts
   1 | import { Page, Locator } from '@playwright/test';
   2 |
   3 | export class LoginPage {
   4 |   readonly page: Page;
   5 |   readonly emailInput: Locator;
   6 |   readonly passwordInput: Locator;
   7 |   readonly submitButton: Locator;
   8 |   readonly signUpLink: Locator;
   9 |   readonly forgotPasswordLink: Locator;
  10 |   readonly errorMessage: Locator;
  11 |
  12 |   constructor(page: Page) {
  13 |     this.page = page;
  14 |     this.emailInput = page.locator('input[type="email"]');
  15 |     this.passwordInput = page.locator('input[type="password"]');
  16 |     this.submitButton = page.locator('button[type="submit"]');
  17 |     this.signUpLink = page.locator('a:has-text("Sign up")');
  18 |     this.forgotPasswordLink = page.locator('a:has-text("Forgot your password")');
  19 |     this.errorMessage = page.locator('.alert-danger');
  20 |   }
  21 |
  22 |   async goto() {
  23 |     await this.page.goto('/cs/users/sign_in');
  24 |   }
  25 |
  26 |   async login(email: string, password: string) {
  27 |     await this.emailInput.fill(email);
  28 |     await this.passwordInput.fill(password);
  29 |     await this.submitButton.click();
  30 |   }
  31 |
  32 |   async expectToBeOnLoginPage() {
  33 |     await this.page.waitForURL('**/cs/users/sign_in');
  34 |   }
  35 |
  36 |   async expectToBeLoggedIn() {
  37 |     // After successful login, should redirect to dashboard with Czech locale
> 38 |     await this.page.waitForURL('**/cs/dashboard', { timeout: 10000 });
     |                     ^ TimeoutError: page.waitForURL: Timeout 10000ms exceeded.
  39 |   }
  40 |
  41 |   async expectErrorMessage(message: string) {
  42 |     await this.errorMessage.waitFor({ state: 'visible' });
  43 |     await this.page.waitForTimeout(100); // Let the message render
  44 |     const errorText = await this.errorMessage.textContent();
  45 |     return errorText?.includes(message) || false;
  46 |   }
  47 | }
```